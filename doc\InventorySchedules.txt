SELECT 
    Stores.Id AS StoreId, 
    Stores.StoreCode, 
    MansionInventoryExecuteDay.InventoryDate
FROM 
    Stores 
    INNER JOIN StoreCodeMapping ON Stores.StoreCode = StoreCodeMapping.StoreCode 
    RIGHT OUTER JOIN MansionInventoryExecuteDay ON StoreCodeMapping.MdStoreCode = MansionInventoryExecuteDay.MansionCode
WHERE 
    MansionInventoryExecuteDay.DelFlg = 0
    AND MansionInventoryExecuteDay.InventoryDate >= DATEADD(DAY, -210, CAST(GETDATE() AS DATE))
    AND MansionInventoryExecuteDay.InventoryDate <= CAST(GETDATE() AS DATE)

// 日次部門別レポート JavaScript

class ReportManager {
    constructor() {
        this.currentData = [];
        this.filterOptions = {};
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.init();
    }

    init() {
        // DOM要素の取得
        this.startDateInput = document.getElementById('startDate');
        this.endDateInput = document.getElementById('endDate');
        this.companySelect = document.getElementById('companySelect');
        this.storeSelect = document.getElementById('storeSelect');
        this.departmentSelect = document.getElementById('departmentSelect');
        this.sectionSelect = document.getElementById('sectionSelect');
        this.searchBtn = document.getElementById('searchBtn');
        this.exportBtn = document.getElementById('exportBtn');
        this.toggleColumnsBtn = document.getElementById('toggleColumnsBtn');
        this.loadingDiv = document.getElementById('loading');
        this.errorDiv = document.getElementById('error');
        this.tableContainer = document.getElementById('tableContainer');
        this.tableWrapper = document.querySelector('.table-wrapper');

        // イベントリスナーの設定
        this.setupEventListeners();

        // 初期日付設定
        this.setDefaultDates();

        // フィルター選択肢読み込み
        this.loadFilterOptions();

        // 高さ調整の設定
        this.setupDynamicHeight();

        // 列表示切り替えボタンの初期テキスト設定
        this.toggleColumnsBtn.textContent = '原価列を表示';
    }

    setupEventListeners() {
        // 検索ボタン
        this.searchBtn.addEventListener('click', () => {
            if (this.validateFilters()) {
                this.loadData();
            }
        });

        // エクスポートボタン
        this.exportBtn.addEventListener('click', () => {
            this.exportToCSV();
        });

        // 列表示切り替えボタン
        this.toggleColumnsBtn.addEventListener('click', () => {
            this.toggleHiddenColumns();
        });

        // 部門選択時の課フィルタ更新
        this.departmentSelect.addEventListener('change', () => {
            this.updateSectionOptions();
        });

        // ソート機能のイベントリスナー
        this.setupSortListeners();

        // 終了日変更時に20日に自動設定
        this.endDateInput.addEventListener('change', () => {
            this.updateEndDateTo20th();
        });

        // Enterキーでの検索
        this.endDateInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && this.validateFilters()) this.loadData();
        });
    }

    toggleHiddenColumns() {
        // 非表示列の表示/非表示を切り替え
        const hiddenColumns = document.querySelectorAll('.hidden-column');
        const isCurrentlyHidden = hiddenColumns.length > 0 && hiddenColumns[0].style.display === 'none' ||
                                  hiddenColumns.length > 0 && window.getComputedStyle(hiddenColumns[0]).display === 'none';

        hiddenColumns.forEach(column => {
            if (isCurrentlyHidden) {
                column.style.display = '';
            } else {
                column.style.display = 'none';
            }
        });

        // データ行の対応する列も切り替え
        const tableBody = document.getElementById('reportTableBody');
        if (tableBody) {
            const rows = tableBody.querySelectorAll('tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                // 仕入原価(10番目)、返品原価(12番目)、出庫原価(14番目)、入庫原価(16番目)の列を切り替え
                const hiddenCellIndices = [10, 12, 14, 16]; // 0-based index
                hiddenCellIndices.forEach(index => {
                    if (cells[index]) {
                        if (isCurrentlyHidden) {
                            cells[index].style.display = '';
                        } else {
                            cells[index].style.display = 'none';
                        }
                    }
                });
            });
        }

        // ボタンのテキストを更新
        this.toggleColumnsBtn.textContent = isCurrentlyHidden ? '原価列を非表示' : '原価列を表示';
    }

    setupDynamicHeight() {
        // 初期高さ設定
        this.adjustTableHeight();

        // ウィンドウリサイズ時の高さ調整
        window.addEventListener('resize', () => {
            this.adjustTableHeight();
        });
    }

    adjustTableHeight() {
        if (!this.tableWrapper) return;

        // 各要素の高さを計算
        const header = document.querySelector('.header');
        const filterSection = document.querySelector('.filter-section');
        const container = document.querySelector('.container');

        let usedHeight = 0;

        // コンテナのパディング
        if (container) {
            const containerStyle = window.getComputedStyle(container);
            usedHeight += parseFloat(containerStyle.paddingTop) + parseFloat(containerStyle.paddingBottom);
        }

        // ヘッダーの高さ
        if (header) {
            usedHeight += header.offsetHeight;
            const headerStyle = window.getComputedStyle(header);
            usedHeight += parseFloat(headerStyle.marginBottom);
        }

        // フィルター部分の高さ
        if (filterSection) {
            usedHeight += filterSection.offsetHeight;
            const filterStyle = window.getComputedStyle(filterSection);
            usedHeight += parseFloat(filterStyle.marginBottom);
        }

        // 余白を追加（安全マージン）
        usedHeight += 40;

        // 利用可能な高さを計算
        const availableHeight = window.innerHeight - usedHeight;
        const minHeight = 400;
        const maxHeight = Math.max(availableHeight, minHeight);

        // テーブルラッパーの高さを設定
        this.tableWrapper.style.maxHeight = `${maxHeight}px`;
        this.tableWrapper.style.minHeight = `${minHeight}px`;
    }

    setDefaultDates() {
        // 終了日のみ設定（開始日は棚卸スケジュールから自動決定）
        const today = new Date();

        // 終了日はシステム日付の当月20日に設定
        const endDate = this.getDateTo20th(today);
        this.endDateInput.value = this.formatDate(endDate);
    }

    formatDate(date) {
        // ローカルタイムゾーン（JST）でフォーマット
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    updateEndDateTo20th() {
        // 終了日を指定された日付の月の20日に設定（無限ループ防止）
        const endDateValue = this.endDateInput.value;
        if (endDateValue) {
            const targetDate = this.getDateTo20th(endDateValue);
            const targetDateString = this.formatDate(targetDate);

            // 既に20日になっている場合は何もしない（無限ループ防止）
            if (endDateValue !== targetDateString) {
                this.endDateInput.value = targetDateString;
            }
        }
    }

    getDateTo20th(inputDate) {
        // 指定された日付の月の20日を取得（ローカルタイムゾーン基準）
        let year, month;

        if (typeof inputDate === 'string') {
            // 文字列の場合（YYYY-MM-DD形式）
            const parts = inputDate.split('-');
            year = parseInt(parts[0]);
            month = parseInt(parts[1]) - 1; // 0-based month
        } else {
            // Dateオブジェクトの場合
            year = inputDate.getFullYear();
            month = inputDate.getMonth(); // 0-based month
        }

        // その月の20日を作成（ローカルタイムゾーン）
        const result = new Date(year, month, 20);

        // デバッグ情報
        console.log('getDateTo20th - Input:', inputDate);
        console.log('getDateTo20th - Year:', year, 'Month:', month);
        console.log('getDateTo20th - Result:', result);
        console.log('getDateTo20th - Formatted:', this.formatDate(result));

        return result;
    }

    async loadFilterOptions() {
        try {
            const response = await fetch('/api/filter-options');
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'フィルター選択肢の取得に失敗しました。');
            }

            this.filterOptions = result.data;
            this.populateFilterOptions();

        } catch (error) {
            console.error('フィルター選択肢読み込みエラー:', error);
            this.showError(error.message);
        }
    }

    populateFilterOptions() {
        // 法人選択肢
        this.companySelect.innerHTML = '<option value="未指定">未指定</option>';
        this.filterOptions.companies.forEach(company => {
            const option = document.createElement('option');
            option.value = company;
            option.textContent = company;
            this.companySelect.appendChild(option);
        });

        // 店舗選択肢
        this.storeSelect.innerHTML = '<option value="未指定">未指定</option>';
        this.filterOptions.stores.forEach(store => {
            const option = document.createElement('option');
            option.value = store;
            option.textContent = store;
            this.storeSelect.appendChild(option);
        });

        // 部門選択肢
        this.departmentSelect.innerHTML = '<option value="未指定">未指定</option>';
        this.filterOptions.departments.forEach(department => {
            const option = document.createElement('option');
            option.value = department;
            option.textContent = department;
            this.departmentSelect.appendChild(option);
        });

        // 初期データ読み込み
        this.loadData();
    }

    updateSectionOptions() {
        const selectedDepartment = this.departmentSelect.value;

        // 課選択肢をクリア
        this.sectionSelect.innerHTML = '<option value="未指定">未指定</option>';

        if (selectedDepartment === '未指定') {
            this.sectionSelect.disabled = true;
            return;
        }

        // 選択された部門の課を取得
        const deptCode = selectedDepartment.split(':')[0];
        const sectionData = this.filterOptions.sections[deptCode];

        if (sectionData && sectionData.sections) {
            this.sectionSelect.disabled = false;
            sectionData.sections.forEach(section => {
                const option = document.createElement('option');
                option.value = section;
                option.textContent = section;
                this.sectionSelect.appendChild(option);
            });
        } else {
            this.sectionSelect.disabled = true;
        }
    }

    validateFilters() {
        // 課が指定されている場合、部門も指定されている必要がある
        const sectionValue = this.sectionSelect.value;
        const departmentValue = this.departmentSelect.value;

        if (sectionValue !== '未指定' && departmentValue === '未指定') {
            this.showError('課を指定する場合は、部門の指定が必須です。');
            return false;
        }

        return true;
    }

    setupSortListeners() {
        // ソート可能なヘッダーにクリックイベントを追加
        document.addEventListener('click', (e) => {
            if (e.target.closest('th.sortable')) {
                const th = e.target.closest('th.sortable');
                const column = th.getAttribute('data-column');
                const dataType = th.getAttribute('data-type');
                this.sortTable(column, dataType, th);
            }
        });
    }

    sortTable(column, dataType, headerElement) {
        // ソート方向の決定
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortDirection = 'asc';
            this.sortColumn = column;
        }

        // ヘッダーのスタイル更新
        this.updateSortHeaders(headerElement);

        // データのソート
        this.currentData.sort((a, b) => {
            let valueA = a[column];
            let valueB = b[column];

            // データタイプに応じた比較
            if (dataType === 'number') {
                valueA = parseFloat(valueA) || 0;
                valueB = parseFloat(valueB) || 0;
            } else {
                valueA = String(valueA || '').toLowerCase();
                valueB = String(valueB || '').toLowerCase();
            }

            let comparison = 0;
            if (valueA > valueB) {
                comparison = 1;
            } else if (valueA < valueB) {
                comparison = -1;
            }

            return this.sortDirection === 'desc' ? comparison * -1 : comparison;
        });

        // テーブルの再描画
        this.renderTable(this.currentData);
    }

    updateSortHeaders(activeHeader) {
        // すべてのソートクラスをクリア
        document.querySelectorAll('th.sortable').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });

        // アクティブなヘッダーにソートクラスを追加
        if (this.sortDirection === 'asc') {
            activeHeader.classList.add('sort-asc');
        } else {
            activeHeader.classList.add('sort-desc');
        }
    }

    resetSort() {
        // ソート状態をリセット
        this.sortColumn = null;
        this.sortDirection = 'asc';

        // すべてのソートクラスをクリア
        document.querySelectorAll('th.sortable').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
    }

    showLoading() {
        this.loadingDiv.style.display = 'block';
        this.errorDiv.style.display = 'none';
        this.tableContainer.style.display = 'none';
    }

    hideLoading() {
        this.loadingDiv.style.display = 'none';
    }

    showError(message) {
        this.errorDiv.innerHTML = `<strong>エラー:</strong> ${message}`;
        this.errorDiv.style.display = 'block';
        this.tableContainer.style.display = 'none';
    }

    showTable() {
        this.errorDiv.style.display = 'none';
        this.tableContainer.style.display = 'block';

        // テーブル表示後に高さを再調整
        setTimeout(() => {
            this.adjustTableHeight();
        }, 100);
    }

    async loadData() {
        try {
            this.showLoading();

            const endDate = this.endDateInput.value;

            // 日付バリデーション
            if (!endDate) {
                throw new Error('終了日を入力してください。');
            }

            // 絞り込みパラメータの構築（開始日は除外）
            const params = new URLSearchParams({
                end_date: endDate
            });

            // 絞り込み条件を追加
            const company = this.companySelect.value;
            const store = this.storeSelect.value;
            const department = this.departmentSelect.value;
            const section = this.sectionSelect.value;

            if (company && company !== '未指定') {
                params.append('company', company);
            }
            if (store && store !== '未指定') {
                params.append('store', store);
            }
            if (department && department !== '未指定') {
                params.append('department', department);
            }
            if (section && section !== '未指定') {
                params.append('section', section);
            }

            // APIリクエスト
            const response = await fetch(`/api/report?${params.toString()}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'データの取得に失敗しました。');
            }

            this.currentData = result.data;
            this.resetSort();
            this.renderTable(result.data);
            this.showTable();

        } catch (error) {
            console.error('データ読み込みエラー:', error);
            this.showError(error.message);
        } finally {
            this.hideLoading();
        }
    }

    renderTable(data) {
        const tableBody = document.getElementById('reportTableBody');

        if (data.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="21" style="text-align: center; padding: 40px;">データがありません</td></tr>';
            return;
        }

        const rows = data.map(row => `
            <tr>
                <td>${row.店舗コード}</td>
                <td style="text-align: left;">${row.店舗名}</td>
                <td>${row.部門コード}</td>
                <td style="text-align: left;">${row.部門名称}</td>
                <td>${row.課コード}</td>
                <td style="text-align: left;">${row.課名称}</td>
                <td style="text-align: center;">${row.前回棚卸日 || '-'}</td>
                <td class="number">${this.formatNumber(row.単品棚卸売価)}</td>
                <td class="number">${this.formatNumber(row.部門棚卸売価)}</td>
                <td class="number">${this.formatNumber(row.売上金額)}</td>
                <td class="number">${this.formatNumber(row.仕入原価)}</td>
                <td class="number">${this.formatNumber(row.仕入売価)}</td>
                <td class="number">${this.formatNumber(row.返品原価)}</td>
                <td class="number">${this.formatNumber(row.返品売価)}</td>
                <td class="number">${this.formatNumber(row.出庫原価)}</td>
                <td class="number">${this.formatNumber(row.出庫売価)}</td>
                <td class="number">${this.formatNumber(row.入庫原価)}</td>
                <td class="number">${this.formatNumber(row.入庫売価)}</td>
                <td class="number">${this.formatNumber(row.廃棄金額)}</td>
                <td class="number">${this.formatNumber(row.値引金額)}</td>
                <td class="number">${this.formatNumber(row.在庫原価)}</td>
            </tr>
        `).join('');

        tableBody.innerHTML = rows;

        // 非表示列の状態を適用
        this.applyHiddenColumnState();
    }

    applyHiddenColumnState() {
        // ヘッダーの非表示列の状態を確認
        const hiddenColumns = document.querySelectorAll('.hidden-column');
        const isHidden = hiddenColumns.length > 0 && window.getComputedStyle(hiddenColumns[0]).display === 'none';

        if (isHidden) {
            // データ行の対応する列も非表示にする
            const tableBody = document.getElementById('reportTableBody');
            if (tableBody) {
                const rows = tableBody.querySelectorAll('tr');
                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    // 仕入原価(10番目)、返品原価(12番目)、出庫原価(14番目)、入庫原価(16番目)の列を非表示
                    const hiddenCellIndices = [10, 12, 14, 16]; // 0-based index
                    hiddenCellIndices.forEach(index => {
                        if (cells[index]) {
                            cells[index].style.display = 'none';
                        }
                    });
                });
            }
        }
    }

    formatNumber(value) {
        if (value === null || value === undefined || value === 0) {
            return '-';
        }
        return new Intl.NumberFormat('ja-JP').format(value);
    }



    exportToCSV() {
        if (this.currentData.length === 0) {
            alert('エクスポートするデータがありません。');
            return;
        }

        // CSVヘッダー（棚卸実績を課名称の後に配置）
        const headers = [
            '店舗コード', '店舗名', '部門コード', '部門名称', '課コード', '課名称',
            '前回棚卸日', '単品棚卸売価', '部門棚卸売価',
            '売上金額(期間合計)', '仕入原価(期間合計)', '仕入売価(期間合計)', '返品原価(期間合計)', '返品売価(期間合計)',
            '出庫原価(期間合計)', '出庫売価(期間合計)', '入庫原価(期間合計)', '入庫売価(期間合計)',
            '廃棄金額(期間合計)', '値引金額(期間合計)', '在庫原価(期間合計)'
        ];

        // CSVデータ作成
        const csvContent = [
            headers.join(','),
            ...this.currentData.map(row => [
                row.店舗コード, `"${row.店舗名}"`, row.部門コード, `"${row.部門名称}"`,
                row.課コード, `"${row.課名称}"`, row.前回棚卸日 || '', row.単品棚卸売価, row.部門棚卸売価,
                row.売上金額, row.仕入原価, row.仕入売価, row.返品原価, row.返品売価,
                row.出庫原価, row.出庫売価, row.入庫原価, row.入庫売価,
                row.廃棄金額, row.値引金額, row.在庫原価
            ].join(','))
        ].join('\n');

        // ファイルダウンロード
        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        
        const endDate = this.endDateInput.value.replace(/-/g, '');
        const filename = `部門別期間合計レポート_${endDate}.csv`;
        
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// ページ読み込み完了後に初期化
document.addEventListener('DOMContentLoaded', () => {
    new ReportManager();
});

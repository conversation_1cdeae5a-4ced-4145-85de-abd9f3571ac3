# グリッド列順変更実装

## 概要
グリッドの列順を変更し、前回棚卸日、単品棚卸売価、部門棚卸売価の3つの列を「課名称」の右側に移動しました。

## 変更内容

### 1. 新しい列順

#### 変更前
```
店舗コード | 店舗名 | 部門コード | 部門名称 | 課コード | 課名称 | 
売上金額 | 仕入原価 | 仕入売価 | 返品原価 | 返品売価 | 
出庫原価 | 出庫売価 | 入庫原価 | 入庫売価 | 廃棄金額 | 
値引金額 | 在庫原価 | 前回棚卸日 | 単品棚卸売価 | 部門棚卸売価
```

#### 変更後
```
店舗コード | 店舗名 | 部門コード | 部門名称 | 課コード | 課名称 | 
前回棚卸日 | 単品棚卸売価 | 部門棚卸売価 |
売上金額 | 仕入原価 | 仕入売価 | 返品原価 | 返品売価 | 
出庫原価 | 出庫売価 | 入庫原価 | 入庫売価 | 廃棄金額 | 
値引金額 | 在庫原価
```

### 2. 修正ファイル

#### templates/report.html
- テーブルヘッダーの列順を変更
- 棚卸関連の3列を「課名称」の後に移動
- 元の位置にあった重複列を削除

#### static/js/report.js
- `renderTable`関数のデータ表示順序を変更
- CSV出力のヘッダー順序を変更
- CSV出力のデータ順序を変更
- テーブル情報表示を「終了日のみ」に変更
- CSVファイル名を終了日のみに変更

### 3. 期待される効果

#### 📊 **データ確認の効率化**
- 基本情報（店舗・部門・課）の直後に棚卸情報を配置
- 棚卸日と棚卸実績を一目で確認可能
- 実績データとの関連性が明確

#### 🎯 **ユーザビリティの向上**
- 論理的な情報の流れ：基本情報 → 棚卸情報 → 実績データ
- 棚卸関連データの集約により確認作業が効率化
- 左から右への自然な読み取り順序

#### 💼 **業務効率の向上**
- 棚卸日の確認が容易
- 棚卸実績と期間実績の比較が簡単
- データの整合性チェックが効率的

### 4. 技術的詳細

#### HTMLテーブル構造
```html
<th data-column="課名称">課名称</th>
<th data-column="前回棚卸日">前回棚卸日</th>
<th data-column="単品棚卸売価">単品棚卸売価</th>
<th data-column="部門棚卸売価">部門棚卸売価</th>
<th data-column="売上金額">売上金額</th>
<!-- 以下実績データ列 -->
```

#### JavaScript表示順序
```javascript
<td>${row.課名称}</td>
<td>${row.前回棚卸日 || '-'}</td>
<td class="number">${this.formatNumber(row.単品棚卸売価)}</td>
<td class="number">${this.formatNumber(row.部門棚卸売価)}</td>
<td class="number">${this.formatNumber(row.売上金額)}</td>
<!-- 以下実績データ -->
```

#### CSV出力順序
```javascript
const headers = [
    '店舗コード', '店舗名', '部門コード', '部門名称', '課コード', '課名称',
    '前回棚卸日', '単品棚卸売価', '部門棚卸売価',
    '売上金額(期間合計)', '仕入原価(期間合計)', /* ... */
];
```

### 5. 互換性

- 既存のソート機能は全て維持
- フィルタリング機能は影響なし
- レスポンシブデザインは維持
- 印刷機能は正常動作

### 6. 注意事項

- 列数は変更なし（21列のまま）
- データの内容は変更なし
- 既存のCSSスタイルは全て適用
- 棚卸関連列の特別スタイルも維持

この変更により、ユーザーは基本情報を確認した直後に棚卸関連情報を確認でき、その後で詳細な実績データを確認するという自然な流れでデータを読み取ることができるようになりました。

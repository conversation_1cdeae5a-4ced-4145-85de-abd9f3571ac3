# 在庫レポート修正内容

## 概要
在庫レポートの検索条件から「開始日」を除外し、代わりに棚卸スケジュールから店舗別の開始日を自動決定する機能を実装しました。

## 主な変更点

### 1. 検索条件の変更
- **開始日入力フィールドを非表示化**
- **終了日のみで検索を実行**
- 開始日は棚卸スケジュールから自動決定される旨をユーザーに説明

### 2. 棚卸スケジュール連携
- `doc\InventorySchedules.txt`のクエリを使用して店舗別の最終棚卸日を取得
- 指定された終了日から210日前以降、終了日以前の最終棚卸日を取得
- 最終棚卸日の翌日を「店舗別の開始日」として設定

### 3. データ表示の変更
- **「棚卸日」→「前回棚卸日」に列名変更**
- 前回棚卸日の棚卸実績（CalcInventoryItemPrice、InventoryDeptPrice）を表示
- 該当期間中に棚卸スケジュールが取得できない店舗は表示対象外

## 修正ファイル

### app.py
- `/api/report`エンドポイントの修正
- 開始日パラメータの除去
- 棚卸スケジュール取得ロジックの追加
- 店舗別開始日の動的計算
- SQLクエリの修正（CTEを使用した棚卸スケジュール連携）

### templates/report.html
- 開始日入力フィールドの非表示化
- 説明文の追加
- 「前回棚卸日」列名の変更

### static/js/report.js
- 開始日関連のイベントリスナー削除
- 日付バリデーションの簡素化
- API呼び出しパラメータの修正
- テーブル表示での列名変更対応

## 新しい動作フロー

1. ユーザーが終了日のみを指定
2. システムが棚卸スケジュールから店舗別の最終棚卸日を取得
3. 最終棚卸日の翌日を店舗別開始日として設定
4. 店舗別開始日から指定終了日までの実績を合算
5. 前回棚卸日の棚卸実績を併せて表示

## 注意事項
- 棚卸スケジュールが存在しない店舗は表示されません
- 210日前以降の棚卸スケジュールのみが対象となります
- 前回棚卸日のデータがない場合は空欄で表示されます

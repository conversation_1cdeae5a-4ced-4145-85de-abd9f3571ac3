#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Daily Department Report Web Application
日次部門別レポートWebアプリケーション
"""

import os
import json
import logging
from datetime import datetime
from flask import Flask, render_template, request, jsonify
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

app = Flask(__name__)

# グローバル変数
engine = None
Session = None
config = None


def load_config():
    """設定ファイル読み込み"""
    global config
    try:
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config_web.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except FileNotFoundError:
        raise Exception("config_web.jsonが見つかりません")
    except json.JSONDecodeError as e:
        raise Exception(f"config_web.jsonの形式が正しくありません: {e}")


def setup_database():
    """データベース設定"""
    global engine, Session
    
    db_config = config['database']
    connection_string = (
        f"mssql+pyodbc://{db_config['username']}:{db_config['password']}"
        f"@{db_config['server']}/{db_config['database']}"
        f"?driver={db_config['driver'].replace(' ', '+')}"
        f"&TrustServerCertificate=yes"
    )
    
    engine = create_engine(connection_string, echo=False)
    Session = sessionmaker(bind=engine)


@app.route('/')
def index():
    """メインページ"""
    return render_template('report.html')


@app.route('/api/filter-options')
def get_filter_options():
    """絞り込み選択肢取得API"""
    try:
        session = Session()

        # 絞り込み選択肢取得クエリ
        query = text("""
            SELECT DISTINCT
                Companies.CompanyCode + ':' + Companies.CompanyName AS 法人,
                Stores.StoreCode + ':' + Stores.StoreName AS 店舗,
                Sections_1.SectionCode + ':' + Sections_1.SectionName AS 部門,
                Sections.SectionCode + ':' + Sections.SectionName AS 課,
                Companies.CompanyCode,
                Stores.StoreCode,
                Sections_1.SectionCode AS 部門コード,
                Sections.SectionCode AS 課コード,
                Sections_1.Id AS 部門Id,
                Sections.Id AS 課Id
            FROM CompanyStoreSectionMapping
            INNER JOIN Sections ON CompanyStoreSectionMapping.SectionId = Sections.Id
            INNER JOIN Sections AS Sections_1 ON Sections.ParentCodeId = Sections_1.Id
            INNER JOIN Companies ON CompanyStoreSectionMapping.CompanyId = Companies.Id
            INNER JOIN Stores ON CompanyStoreSectionMapping.StoreId = Stores.Id
            WHERE (Companies.DelFlg = 0)
                AND (CompanyStoreSectionMapping.DelFlg = 0)
                AND (Sections.DelFlg = 0)
                AND (Sections_1.DelFlg = 0)
                AND (Stores.DelFlg = 0)
            ORDER BY Companies.CompanyCode, Stores.StoreCode, Sections_1.SectionCode, Sections.SectionCode
        """)

        result = session.execute(query)

        # 選択肢を整理
        companies = set()
        stores = set()
        departments = set()
        sections = {}  # 部門IDをキーとして課を管理

        for row in result:
            companies.add(row[0])  # 法人
            stores.add(row[1])     # 店舗
            departments.add(row[2]) # 部門

            # 課は部門IDごとに管理
            dept_id = row[8]  # 部門Id
            if dept_id not in sections:
                sections[dept_id] = {
                    'dept_code': row[6],  # 部門コード
                    'dept_name': row[2],  # 部門名
                    'sections': set()
                }
            sections[dept_id]['sections'].add(row[3])  # 課

        session.close()

        # レスポンス用にデータを整形
        response_data = {
            'companies': sorted(list(companies)),
            'stores': sorted(list(stores)),
            'departments': sorted(list(departments)),
            'sections': {}
        }

        # 課データを部門コード別に整理
        for dept_id, dept_data in sections.items():
            dept_code = dept_data['dept_code']
            response_data['sections'][dept_code] = {
                'dept_name': dept_data['dept_name'],
                'sections': sorted(list(dept_data['sections']))
            }

        return jsonify({
            'success': True,
            'data': response_data
        })

    except Exception as e:
        if 'session' in locals():
            session.close()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/report')
def get_report_data():
    """レポートデータAPI"""
    try:
        # パラメータ取得（開始日は除外、終了日のみ使用）
        end_date = request.args.get('end_date')
        company_filter = request.args.get('company')
        store_filter = request.args.get('store')
        department_filter = request.args.get('department')
        section_filter = request.args.get('section')

        # デフォルト終了日設定（当月20日）
        if not end_date:
            today = datetime.now().date()
            end_date = today.replace(day=20)
        else:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

        # 絞り込み条件の構築
        filter_conditions = []
        filter_params = {
            'end_date': end_date
        }

        if company_filter and company_filter != '未指定':
            company_code = company_filter.split(':')[0]
            filter_conditions.append("Companies.CompanyCode = :company_code")
            filter_params['company_code'] = company_code

        if store_filter and store_filter != '未指定':
            store_code = store_filter.split(':')[0]
            filter_conditions.append("Stores.StoreCode = :store_code")
            filter_params['store_code'] = store_code

        if department_filter and department_filter != '未指定':
            dept_code = department_filter.split(':')[0]
            filter_conditions.append("Sections_1.SectionCode = :dept_code")
            filter_params['dept_code'] = dept_code

        if section_filter and section_filter != '未指定':
            section_code = section_filter.split(':')[0]
            filter_conditions.append("Sections.SectionCode = :section_code")
            filter_params['section_code'] = section_code

        # 追加の絞り込み条件をWHERE句に追加
        additional_filters = ""
        if filter_conditions:
            additional_filters = " AND " + " AND ".join(filter_conditions)
        
        # SQLクエリ実行
        session = Session()

        # 棚卸スケジュールから店舗別の開始日を取得するクエリ
        inventory_schedule_query = text("""
            WITH StoreInventorySchedule AS (
                SELECT
                    Stores.Id AS StoreId,
                    Stores.StoreCode,
                    Stores.StoreName,
                    MAX(MansionInventoryExecuteDay.InventoryDate) AS LastInventoryDate
                FROM
                    Stores
                    INNER JOIN StoreCodeMapping ON Stores.StoreCode = StoreCodeMapping.StoreCode
                    RIGHT OUTER JOIN MansionInventoryExecuteDay ON StoreCodeMapping.MdStoreCode = MansionInventoryExecuteDay.MansionCode
                WHERE
                    MansionInventoryExecuteDay.DelFlg = 0
                    AND MansionInventoryExecuteDay.InventoryDate >= DATEADD(DAY, -210, :end_date)
                    AND MansionInventoryExecuteDay.InventoryDate <= :end_date
                    AND Stores.DelFlg = 0
                GROUP BY Stores.Id, Stores.StoreCode, Stores.StoreName
            )
            SELECT
                StoreId,
                StoreCode,
                StoreName,
                LastInventoryDate,
                DATEADD(DAY, 1, LastInventoryDate) AS StoreStartDate
            FROM StoreInventorySchedule
        """)

        # 店舗別の棚卸スケジュールを取得
        inventory_schedules = session.execute(inventory_schedule_query, {'end_date': end_date}).fetchall()

        if not inventory_schedules:
            # 該当期間に棚卸スケジュールがない場合は空のデータを返す
            return jsonify({
                'success': True,
                'data': [],
                'end_date': end_date.strftime('%Y-%m-%d'),
                'total_records': 0,
                'message': '指定期間内に棚卸スケジュールが存在する店舗がありません。'
            })

        # 新しいクエリでは店舗別の日付フィルタはCTE内で処理されるため、個別パラメータは不要

        # 期間合計クエリ（店舗・部門・課別の合計値）with絞り込み条件 + 前回棚卸実績
        # 実績が存在する場合は必ず前回棚卸日も表示されるように修正
        query = text(f"""
            WITH StoreInventorySchedule AS (
                SELECT
                    Stores.Id AS StoreId,
                    MAX(MansionInventoryExecuteDay.InventoryDate) AS LastInventoryDate,
                    DATEADD(DAY, 1, MAX(MansionInventoryExecuteDay.InventoryDate)) AS StoreStartDate
                FROM
                    Stores
                    INNER JOIN StoreCodeMapping ON Stores.StoreCode = StoreCodeMapping.StoreCode
                    RIGHT OUTER JOIN MansionInventoryExecuteDay ON StoreCodeMapping.MdStoreCode = MansionInventoryExecuteDay.MansionCode
                WHERE
                    MansionInventoryExecuteDay.DelFlg = 0
                    AND MansionInventoryExecuteDay.InventoryDate >= DATEADD(DAY, -210, :end_date)
                    AND MansionInventoryExecuteDay.InventoryDate <= :end_date
                    AND Stores.DelFlg = 0
                GROUP BY Stores.Id
            )
            SELECT
                Stores.StoreCode AS 店舗コード,
                Stores.StoreName AS 店舗名,
                Sections_1.SectionCode AS 部門コード,
                Sections_1.SectionName AS 部門名称,
                Sections.SectionCode AS 課コード,
                Sections.SectionName AS 課名称,
                SUM(ISNULL(DailyDeptResults.SalesAmount, 0)) AS 売上金額,
                SUM(ISNULL(DailyDeptResults.PurchaseAmount, 0)) AS 仕入原価,
                SUM(ISNULL(DailyDeptPriceResults.PurchasePriceSum, 0)) AS 仕入売価,
                SUM(ISNULL(DailyDeptResults.ReturnAmount, 0)) AS 返品原価,
                SUM(ISNULL(DailyDeptPriceResults.ReturnPriceSum, 0)) AS 返品売価,
                SUM(ISNULL(DailyDeptResults.TransferOutAmount, 0)) AS 出庫原価,
                SUM(ISNULL(DailyDeptPriceResults.OutPriceSum, 0)) AS 出庫売価,
                SUM(ISNULL(DailyDeptResults.TransferInAmount, 0)) AS 入庫原価,
                SUM(ISNULL(DailyDeptPriceResults.InPriceSum, 0)) AS 入庫売価,
                SUM(ISNULL(DailyDeptResults.DisposalAmountPrice, 0)) AS 廃棄金額,
                SUM(ISNULL(DailyDeptResults.DiscountAmount, 0)) AS 値引金額,
                SUM(ISNULL(DailyDeptResults.InventoryAomunt, 0)) AS 在庫原価,
                StoreInventorySchedule.LastInventoryDate AS 前回棚卸日,
                ISNULL(PrevInventory.CalcInventoryItemPrice, 0) AS 単品棚卸売価,
                ISNULL(PrevInventory.InventoryDeptPrice, 0) AS 部門棚卸売価
            FROM
                StoreInventorySchedule
                INNER JOIN Stores ON StoreInventorySchedule.StoreId = Stores.Id
                INNER JOIN DailyDeptResults
                    ON Stores.Id = DailyDeptResults.StoreId
                    AND DailyDeptResults.SalesDate BETWEEN StoreInventorySchedule.StoreStartDate AND :end_date
                INNER JOIN Sections
                    ON DailyDeptResults.SectionId = Sections.Id
                INNER JOIN Sections AS Sections_1
                    ON Sections.ParentCodeId = Sections_1.Id
                LEFT OUTER JOIN DailyDeptPriceResults
                    ON DailyDeptResults.StoreId = DailyDeptPriceResults.StoreId
                    AND DailyDeptResults.SectionId = DailyDeptPriceResults.SectionId
                    AND DailyDeptResults.SalesDate = DailyDeptPriceResults.SalesDate
                LEFT OUTER JOIN CompanyStoreSectionMapping
                    ON Stores.Id = CompanyStoreSectionMapping.StoreId
                    AND Sections.Id = CompanyStoreSectionMapping.SectionId
                LEFT OUTER JOIN Companies
                    ON CompanyStoreSectionMapping.CompanyId = Companies.Id
                LEFT OUTER JOIN DeptInventory AS PrevInventory
                    ON Stores.Id = PrevInventory.StoreId
                    AND Sections.Id = PrevInventory.SectionId
                    AND PrevInventory.InventoryDate = StoreInventorySchedule.LastInventoryDate
            WHERE
                Stores.DelFlg = 0
                AND Sections_1.DelFlg = 0
                AND Sections.DelFlg = 0
                {additional_filters}
            GROUP BY
                Stores.StoreCode,
                Stores.StoreName,
                Sections_1.SectionCode,
                Sections_1.SectionName,
                Sections.SectionCode,
                Sections.SectionName,
                StoreInventorySchedule.LastInventoryDate,
                PrevInventory.CalcInventoryItemPrice,
                PrevInventory.InventoryDeptPrice
            ORDER BY
                Stores.StoreCode,
                Sections_1.SectionCode,
                Sections.SectionCode
        """)
        
        result = session.execute(query, filter_params)
        
        # 結果をJSON形式に変換（期間合計データ + 棚卸実績）
        data = []
        for row in result:
            data.append({
                '店舗コード': row[0],
                '店舗名': row[1],
                '部門コード': row[2],
                '部門名称': row[3],
                '課コード': row[4],
                '課名称': row[5],
                '売上金額': float(row[6]) if row[6] else 0,
                '仕入原価': float(row[7]) if row[7] else 0,
                '仕入売価': float(row[8]) if row[8] else 0,
                '返品原価': float(row[9]) if row[9] else 0,
                '返品売価': float(row[10]) if row[10] else 0,
                '出庫原価': float(row[11]) if row[11] else 0,
                '出庫売価': float(row[12]) if row[12] else 0,
                '入庫原価': float(row[13]) if row[13] else 0,
                '入庫売価': float(row[14]) if row[14] else 0,
                '廃棄金額': float(row[15]) if row[15] else 0,
                '値引金額': float(row[16]) if row[16] else 0,
                '在庫原価': float(row[17]) if row[17] else 0,
                '前回棚卸日': row[18].strftime('%Y-%m-%d') if row[18] else '',
                '単品棚卸売価': float(row[19]) if row[19] else 0,
                '部門棚卸売価': float(row[20]) if row[20] else 0
            })
        
        session.close()
        
        return jsonify({
            'success': True,
            'data': data,
            'end_date': end_date.strftime('%Y-%m-%d'),
            'total_records': len(data)
        })
        
    except Exception as e:
        if 'session' in locals():
            session.close()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


def main():
    """メイン関数"""
    try:
        # 設定読み込み
        load_config()
        
        # データベース設定
        setup_database()
        
        # Flask設定
        app.secret_key = config['web_settings']['secret_key']
        
        # ログ設定
        logging.basicConfig(level=logging.INFO)
        
        # アプリケーション起動
        app.run(
            host=config['web_settings']['host'],
            port=config['web_settings']['port'],
            debug=config['web_settings']['debug']
        )
        
    except Exception as e:
        print(f"アプリケーション起動エラー: {e}")
        return 1


if __name__ == "__main__":
    main()
